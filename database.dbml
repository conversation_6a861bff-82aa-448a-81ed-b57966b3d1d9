// Building Management System Database Schema
// Multi-tenant architecture with horizontal sharding

Project uni_home_building_management {
  database_type: 'PostgreSQL'
  Note: '''
    Multi-tenant building management system
    Sharding strategy: By building_id for horizontal scaling
    Each building can be on separate database shard
  '''
}

// ===== TENANT & BUILDING MANAGEMENT =====

Table tenants {
  id uuid [primary key, default: `gen_random_uuid()`]
  name varchar(255) [not null]
  domain varchar(100) [unique, not null] // subdomain for multi-tenancy
  subscription_plan varchar(50) [not null, default: 'basic']
  status varchar(20) [not null, default: 'active'] // active, suspended, cancelled
  created_at timestamp [default: `now()`]
  updated_at timestamp [default: `now()`]

  Note: 'Top-level tenant organization'
}

Table buildings {
  id uuid [primary key, default: `gen_random_uuid()`]
  tenant_id uuid [ref: > tenants.id, not null]
  name varchar(255) [not null]
  address text [not null]
  city varchar(100) [not null]
  state varchar(100)
  postal_code varchar(20)
  country varchar(100) [not null]
  total_floors integer [not null]
  total_units integer [not null]
  building_type varchar(50) [not null] // residential, commercial, mixed
  status varchar(20) [not null, default: 'active']
  created_at timestamp [default: `now()`]
  updated_at timestamp [default: `now()`]

  Note: 'Primary sharding key - each building can be on separate shard'
}

// ===== USER MANAGEMENT =====

Table users {
  id uuid [primary key, default: `gen_random_uuid()`]
  tenant_id uuid [ref: > tenants.id, not null]
  email varchar(255) [unique, not null]
  password_hash varchar(255) [not null]
  first_name varchar(100) [not null]
  last_name varchar(100) [not null]
  phone varchar(20)
  role varchar(50) [not null] // super_admin, building_manager, front_desk, maintenance, resident
  status varchar(20) [not null, default: 'active']
  last_login timestamp
  created_at timestamp [default: `now()`]
  updated_at timestamp [default: `now()`]

  indexes {
    (tenant_id, email) [unique]
    (tenant_id, role)
  }
}

Table user_buildings {
  id uuid [primary key, default: `gen_random_uuid()`]
  user_id uuid [ref: > users.id, not null]
  building_id uuid [ref: > buildings.id, not null]
  role varchar(50) [not null]
  created_at timestamp [default: `now()`]

  indexes {
    (user_id, building_id) [unique]
    (building_id, role)
  }

  Note: 'Many-to-many relationship for users and buildings'
}

// ===== UNIT & RESIDENT MANAGEMENT =====

Table units {
  id uuid [primary key, default: `gen_random_uuid()`]
  building_id uuid [ref: > buildings.id, not null]
  unit_number varchar(20) [not null]
  floor_number integer [not null]
  unit_type varchar(50) [not null] // studio, 1br, 2br, 3br, penthouse
  square_footage decimal(8,2)
  bedrooms integer
  bathrooms decimal(3,1)
  monthly_maintenance decimal(10,2) [not null]
  parking_spaces integer [default: 0]
  status varchar(20) [not null, default: 'available'] // available, occupied, maintenance
  created_at timestamp [default: `now()`]
  updated_at timestamp [default: `now()`]

  indexes {
    (building_id, unit_number) [unique]
    (building_id, status)
  }
}

Table residents {
  id uuid [primary key, default: `gen_random_uuid()`]
  user_id uuid [ref: > users.id, not null]
  unit_id uuid [ref: > units.id, not null]
  relationship varchar(50) [not null] // primary, spouse, child, tenant
  move_in_date date [not null]
  move_out_date date
  emergency_contact_name varchar(255)
  emergency_contact_phone varchar(20)
  emergency_contact_relationship varchar(100)
  status varchar(20) [not null, default: 'active']
  created_at timestamp [default: `now()`]
  updated_at timestamp [default: `now()`]

  indexes {
    (unit_id, status)
    (user_id)
  }
}

// ===== INVOICE & FINANCIAL MANAGEMENT =====

Table invoice_templates {
  id uuid [primary key, default: `gen_random_uuid()`]
  building_id uuid [ref: > buildings.id, not null]
  name varchar(255) [not null]
  invoice_type varchar(50) [not null] // maintenance, utilities, parking, amenities
  description text
  amount decimal(10,2)
  is_recurring boolean [default: true]
  recurring_day integer [default: 1] // day of month for recurring invoices
  is_active boolean [default: true]
  created_at timestamp [default: `now()`]
  updated_at timestamp [default: `now()`]

  indexes {
    (building_id, invoice_type)
  }
}

Table invoices {
  id uuid [primary key, default: `gen_random_uuid()`]
  building_id uuid [ref: > buildings.id, not null]
  unit_id uuid [ref: > units.id, not null]
  invoice_number varchar(50) [unique, not null]
  invoice_type varchar(50) [not null]
  description text
  amount decimal(10,2) [not null]
  due_date date [not null]
  issue_date date [not null, default: `current_date`]
  status varchar(20) [not null, default: 'pending'] // pending, paid, overdue, cancelled
  late_fee decimal(10,2) [default: 0]
  paid_amount decimal(10,2) [default: 0]
  paid_date date
  payment_method varchar(50)
  notes text
  created_at timestamp [default: `now()`]
  updated_at timestamp [default: `now()`]

  indexes {
    (building_id, status)
    (unit_id, status)
    (due_date)
    (invoice_number) [unique]
  }
}

Table payments {
  id uuid [primary key, default: `gen_random_uuid()`]
  invoice_id uuid [ref: > invoices.id, not null]
  amount decimal(10,2) [not null]
  payment_method varchar(50) [not null] // cash, check, bank_transfer, credit_card, online
  payment_date date [not null, default: `current_date`]
  transaction_id varchar(255)
  reference_number varchar(255)
  notes text
  status varchar(20) [not null, default: 'completed'] // pending, completed, failed, refunded
  created_at timestamp [default: `now()`]

  indexes {
    (invoice_id)
    (payment_date)
    (transaction_id)
  }
}

// ===== ELEVATOR MANAGEMENT =====

Table elevators {
  id uuid [primary key, default: `gen_random_uuid()`]
  building_id uuid [ref: > buildings.id, not null]
  elevator_number varchar(20) [not null]
  capacity_kg integer [not null]
  capacity_persons integer [not null]
  floors_served text [not null] // JSON array of floor numbers
  status varchar(20) [not null, default: 'operational'] // operational, maintenance, out_of_order
  last_maintenance_date date
  next_maintenance_date date
  installation_date date
  manufacturer varchar(100)
  model varchar(100)
  created_at timestamp [default: `now()`]
  updated_at timestamp [default: `now()`]

  indexes {
    (building_id, elevator_number) [unique]
    (building_id, status)
  }
}

Table elevator_maintenance {
  id uuid [primary key, default: `gen_random_uuid()`]
  elevator_id uuid [ref: > elevators.id, not null]
  maintenance_type varchar(50) [not null] // preventive, corrective, emergency
  scheduled_date date [not null]
  completed_date date
  vendor_id uuid
  technician_name varchar(255)
  description text
  cost decimal(10,2)
  status varchar(20) [not null, default: 'scheduled'] // scheduled, in_progress, completed, cancelled
  notes text
  created_at timestamp [default: `now()`]
  updated_at timestamp [default: `now()`]

  indexes {
    (elevator_id, status)
    (scheduled_date)
  }
}

Table elevator_incidents {
  id uuid [primary key, default: `gen_random_uuid()`]
  elevator_id uuid [ref: > elevators.id, not null]
  reported_by uuid [ref: > users.id]
  incident_type varchar(50) [not null] // breakdown, stuck, noise, safety
  description text [not null]
  severity varchar(20) [not null] // low, medium, high, critical
  reported_at timestamp [not null, default: `now()`]
  resolved_at timestamp
  resolution_notes text
  status varchar(20) [not null, default: 'open'] // open, in_progress, resolved, closed

  indexes {
    (elevator_id, status)
    (reported_at)
    (severity)
  }
}

// ===== ACCESS CARD MANAGEMENT =====

Table access_cards {
  id uuid [primary key, default: `gen_random_uuid()`]
  building_id uuid [ref: > buildings.id, not null]
  card_number varchar(50) [unique, not null]
  card_type varchar(50) [not null] // resident, visitor, staff, contractor, emergency
  holder_id uuid [ref: > users.id] // null for visitor cards
  holder_name varchar(255) [not null]
  issue_date date [not null, default: `current_date`]
  expiry_date date
  status varchar(20) [not null, default: 'active'] // active, inactive, lost, stolen, expired
  access_level varchar(50) [not null] // basic, premium, staff, admin
  notes text
  created_at timestamp [default: `now()`]
  updated_at timestamp [default: `now()`]

  indexes {
    (building_id, card_number) [unique]
    (building_id, status)
    (holder_id)
    (expiry_date)
  }
}

Table access_permissions {
  id uuid [primary key, default: `gen_random_uuid()`]
  card_id uuid [ref: > access_cards.id, not null]
  access_point_id uuid [ref: > access_points.id, not null]
  time_restrictions text // JSON object for time-based access
  valid_from date [not null, default: `current_date`]
  valid_until date
  is_active boolean [default: true]
  created_at timestamp [default: `now()`]

  indexes {
    (card_id, access_point_id) [unique]
    (access_point_id, is_active)
  }
}

Table access_points {
  id uuid [primary key, default: `gen_random_uuid()`]
  building_id uuid [ref: > buildings.id, not null]
  name varchar(255) [not null]
  location varchar(255) [not null]
  access_type varchar(50) [not null] // main_entrance, parking, elevator, amenity, unit
  floor_number integer
  device_id varchar(100) [unique]
  status varchar(20) [not null, default: 'active'] // active, inactive, maintenance
  created_at timestamp [default: `now()`]
  updated_at timestamp [default: `now()`]

  indexes {
    (building_id, access_type)
    (device_id) [unique]
  }
}

Table access_logs {
  id uuid [primary key, default: `gen_random_uuid()`]
  access_point_id uuid [ref: > access_points.id, not null]
  card_id uuid [ref: > access_cards.id]
  card_number varchar(50) [not null]
  access_time timestamp [not null, default: `now()`]
  access_granted boolean [not null]
  denial_reason varchar(255)

  indexes {
    (access_point_id, access_time)
    (card_id, access_time)
    (access_time)
  }

  Note: 'High-volume table - consider partitioning by date'
}

// ===== FACILITY & AMENITY MANAGEMENT =====

Table amenities {
  id uuid [primary key, default: `gen_random_uuid()`]
  building_id uuid [ref: > buildings.id, not null]
  name varchar(255) [not null]
  description text
  location varchar(255)
  capacity integer
  amenity_type varchar(50) [not null] // gym, pool, meeting_room, party_hall, playground
  booking_required boolean [default: false]
  advance_booking_days integer [default: 7]
  max_booking_hours integer [default: 4]
  hourly_rate decimal(8,2) [default: 0]
  operating_hours text // JSON object for daily hours
  status varchar(20) [not null, default: 'available'] // available, maintenance, closed
  created_at timestamp [default: `now()`]
  updated_at timestamp [default: `now()`]

  indexes {
    (building_id, amenity_type)
    (building_id, status)
  }
}

Table amenity_bookings {
  id uuid [primary key, default: `gen_random_uuid()`]
  amenity_id uuid [ref: > amenities.id, not null]
  resident_id uuid [ref: > residents.id, not null]
  booking_date date [not null]
  start_time time [not null]
  end_time time [not null]
  guest_count integer [default: 0]
  total_cost decimal(8,2) [default: 0]
  status varchar(20) [not null, default: 'confirmed'] // confirmed, cancelled, completed
  special_requests text
  created_at timestamp [default: `now()`]
  updated_at timestamp [default: `now()`]

  indexes {
    (amenity_id, booking_date)
    (resident_id, booking_date)
    (booking_date, status)
  }
}

Table maintenance_requests {
  id uuid [primary key, default: `gen_random_uuid()`]
  building_id uuid [ref: > buildings.id, not null]
  unit_id uuid [ref: > units.id]
  requested_by uuid [ref: > users.id, not null]
  assigned_to uuid [ref: > users.id]
  category varchar(50) [not null] // plumbing, electrical, hvac, appliance, general
  priority varchar(20) [not null, default: 'medium'] // low, medium, high, urgent
  title varchar(255) [not null]
  description text [not null]
  location varchar(255)
  preferred_time varchar(100)
  status varchar(20) [not null, default: 'open'] // open, assigned, in_progress, completed, cancelled
  estimated_cost decimal(10,2)
  actual_cost decimal(10,2)
  completion_notes text
  resident_rating integer // 1-5 stars
  resident_feedback text
  created_at timestamp [default: `now()`]
  updated_at timestamp [default: `now()`]
  completed_at timestamp

  indexes {
    (building_id, status)
    (unit_id, status)
    (assigned_to, status)
    (priority, status)
    (created_at)
  }
}

Table vendors {
  id uuid [primary key, default: `gen_random_uuid()`]
  building_id uuid [ref: > buildings.id, not null]
  company_name varchar(255) [not null]
  contact_person varchar(255)
  email varchar(255)
  phone varchar(20)
  address text
  service_categories text // JSON array of service types
  hourly_rate decimal(8,2)
  rating decimal(3,2) // average rating
  is_preferred boolean [default: false]
  status varchar(20) [not null, default: 'active']
  created_at timestamp [default: `now()`]
  updated_at timestamp [default: `now()`]

  indexes {
    (building_id, status)
    (building_id, is_preferred)
  }
}

// ===== COMMUNICATION SYSTEM =====

Table announcements {
  id uuid [primary key, default: `gen_random_uuid()`]
  building_id uuid [ref: > buildings.id, not null]
  created_by uuid [ref: > users.id, not null]
  title varchar(255) [not null]
  content text [not null]
  announcement_type varchar(50) [not null] // general, emergency, maintenance, event
  priority varchar(20) [not null, default: 'normal'] // low, normal, high, urgent
  target_audience varchar(50) [not null, default: 'all'] // all, residents, staff, specific_units
  target_units text // JSON array of unit IDs if specific targeting
  publish_date timestamp [not null, default: `now()`]
  expiry_date timestamp
  is_published boolean [default: true]
  send_email boolean [default: false]
  send_sms boolean [default: false]
  send_push boolean [default: true]
  created_at timestamp [default: `now()`]
  updated_at timestamp [default: `now()`]

  indexes {
    (building_id, announcement_type)
    (building_id, publish_date)
    (priority, is_published)
  }
}

Table messages {
  id uuid [primary key, default: `gen_random_uuid()`]
  building_id uuid [ref: > buildings.id, not null]
  sender_id uuid [ref: > users.id, not null]
  recipient_id uuid [ref: > users.id, not null]
  subject varchar(255)
  content text [not null]
  message_type varchar(50) [not null, default: 'general'] // general, complaint, inquiry, emergency
  is_read boolean [default: false]
  read_at timestamp
  parent_message_id uuid [ref: > messages.id] // for threading
  created_at timestamp [default: `now()`]

  indexes {
    (building_id, recipient_id, is_read)
    (sender_id, created_at)
    (parent_message_id)
  }
}

// ===== VISITOR MANAGEMENT =====

Table visitors {
  id uuid [primary key, default: `gen_random_uuid()`]
  building_id uuid [ref: > buildings.id, not null]
  host_resident_id uuid [ref: > residents.id, not null]
  visitor_name varchar(255) [not null]
  visitor_phone varchar(20)
  visitor_email varchar(255)
  id_document_type varchar(50) // passport, drivers_license, national_id
  id_document_number varchar(100)
  vehicle_number varchar(20)
  visit_purpose varchar(255)
  expected_arrival timestamp [not null]
  expected_departure timestamp
  actual_arrival timestamp
  actual_departure timestamp
  status varchar(20) [not null, default: 'expected'] // expected, arrived, departed, cancelled
  pre_approved boolean [default: false]
  access_card_issued varchar(50)
  notes text
  created_at timestamp [default: `now()`]
  updated_at timestamp [default: `now()`]

  indexes {
    (building_id, status)
    (host_resident_id, expected_arrival)
    (expected_arrival)
    (access_card_issued)
  }
}

Table events {
  id uuid [primary key, default: `gen_random_uuid()`]
  building_id uuid [ref: > buildings.id, not null]
  organizer_id uuid [ref: > users.id, not null]
  title varchar(255) [not null]
  description text
  event_type varchar(50) [not null] // community, maintenance, emergency, social
  location varchar(255)
  start_datetime timestamp [not null]
  end_datetime timestamp [not null]
  max_attendees integer
  registration_required boolean [default: false]
  registration_deadline timestamp
  cost decimal(8,2) [default: 0]
  status varchar(20) [not null, default: 'scheduled'] // scheduled, ongoing, completed, cancelled
  created_at timestamp [default: `now()`]
  updated_at timestamp [default: `now()`]

  indexes {
    (building_id, event_type)
    (building_id, start_datetime)
    (status, start_datetime)
  }
}

Table event_registrations {
  id uuid [primary key, default: `gen_random_uuid()`]
  event_id uuid [ref: > events.id, not null]
  resident_id uuid [ref: > residents.id, not null]
  attendee_count integer [default: 1]
  registration_date timestamp [default: `now()`]
  status varchar(20) [not null, default: 'registered'] // registered, attended, cancelled

  indexes {
    (event_id, resident_id) [unique]
    (event_id, status)
  }
}

// ===== SYSTEM CONFIGURATION =====

Table building_settings {
  id uuid [primary key, default: `gen_random_uuid()`]
  building_id uuid [ref: > buildings.id, not null]
  setting_key varchar(100) [not null]
  setting_value text
  setting_type varchar(50) [not null] // string, number, boolean, json
  description text
  is_system_setting boolean [default: false]
  created_at timestamp [default: `now()`]
  updated_at timestamp [default: `now()`]

  indexes {
    (building_id, setting_key) [unique]
    (building_id, is_system_setting)
  }
}

Table audit_logs {
  id uuid [primary key, default: `gen_random_uuid()`]
  building_id uuid [ref: > buildings.id, not null]
  user_id uuid [ref: > users.id]
  action varchar(100) [not null]
  entity_type varchar(50) [not null] // user, invoice, payment, etc.
  entity_id uuid
  old_values text // JSON
  new_values text // JSON
  ip_address varchar(45)
  user_agent text
  created_at timestamp [default: `now()`]

  indexes {
    (building_id, created_at)
    (user_id, created_at)
    (entity_type, entity_id)
    (action, created_at)
  }

  Note: 'High-volume table - consider partitioning by date'
}

// ===== NOTIFICATION SYSTEM =====

Table notification_templates {
  id uuid [primary key, default: `gen_random_uuid()`]
  building_id uuid [ref: > buildings.id, not null]
  template_name varchar(100) [not null]
  template_type varchar(50) [not null] // email, sms, push
  subject varchar(255)
  content text [not null]
  variables text // JSON array of available variables
  is_active boolean [default: true]
  created_at timestamp [default: `now()`]
  updated_at timestamp [default: `now()`]

  indexes {
    (building_id, template_name) [unique]
    (building_id, template_type)
  }
}

Table notifications {
  id uuid [primary key, default: `gen_random_uuid()`]
  building_id uuid [ref: > buildings.id, not null]
  recipient_id uuid [ref: > users.id, not null]
  notification_type varchar(50) [not null] // email, sms, push
  title varchar(255)
  content text [not null]
  status varchar(20) [not null, default: 'pending'] // pending, sent, delivered, failed
  sent_at timestamp
  delivered_at timestamp
  read_at timestamp
  error_message text
  created_at timestamp [default: `now()`]

  indexes {
    (building_id, recipient_id, status)
    (recipient_id, created_at)
    (status, created_at)
  }

  Note: 'High-volume table - consider partitioning by date'
}