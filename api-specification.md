# Building Management System API Specification

## 1. API Overview

### 1.1 Base URL
```
Production: https://api.unihome.com/v1
Staging: https://staging-api.unihome.com/v1
```

### 1.2 Authentication
- **Type**: JWT Bearer <PERSON>ken
- **Header**: `Authorization: Bearer <token>`
- **Token Expiry**: 24 hours (configurable)
- **Refresh Token**: 30 days

### 1.3 Multi-tenancy
- **Tenant Identification**: Subdomain-based (`{tenant}.unihome.com`)
- **Building Context**: Building ID in request headers or path parameters
- **Data Isolation**: Complete tenant and building-level data separation

## 2. Core API Endpoints

### 2.1 Authentication & User Management

#### POST /auth/login
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "building_id": "uuid" // optional for multi-building users
}
```

#### POST /auth/refresh
```json
{
  "refresh_token": "refresh_token_string"
}
```

#### GET /users/profile
Returns current user profile and permissions

#### PUT /users/profile
Update user profile information

### 2.2 Building Management

#### GET /buildings
List buildings accessible to current user

#### GET /buildings/{building_id}
Get detailed building information

#### GET /buildings/{building_id}/units
List all units in building with filters:
- `status`: available, occupied, maintenance
- `floor`: floor number
- `unit_type`: studio, 1br, 2br, etc.

#### GET /buildings/{building_id}/residents
List residents with pagination and filters

### 2.3 Invoice Management

#### GET /invoices
List invoices with filters:
- `unit_id`: specific unit
- `status`: pending, paid, overdue
- `date_range`: from/to dates
- `invoice_type`: maintenance, utilities, etc.

#### POST /invoices/bulk-generate
Generate invoices for multiple units:
```json
{
  "building_id": "uuid",
  "invoice_template_id": "uuid",
  "unit_ids": ["uuid1", "uuid2"],
  "due_date": "2024-01-31",
  "issue_date": "2024-01-01"
}
```

#### GET /invoices/{invoice_id}
Get detailed invoice information

#### POST /invoices/{invoice_id}/payments
Record payment for invoice:
```json
{
  "amount": 1500.00,
  "payment_method": "bank_transfer",
  "payment_date": "2024-01-15",
  "transaction_id": "TXN123456",
  "reference_number": "REF789"
}
```

### 2.4 Resident Management

#### POST /residents
Register new resident:
```json
{
  "user_id": "uuid",
  "unit_id": "uuid",
  "relationship": "primary",
  "move_in_date": "2024-01-01",
  "emergency_contact": {
    "name": "John Doe",
    "phone": "+**********",
    "relationship": "spouse"
  }
}
```

#### PUT /residents/{resident_id}
Update resident information

#### POST /residents/{resident_id}/move-out
Process resident move-out:
```json
{
  "move_out_date": "2024-12-31",
  "final_invoice_id": "uuid",
  "deposit_refund": 1000.00
}
```

### 2.5 Elevator Management

#### GET /elevators
List elevators in building with status

#### GET /elevators/{elevator_id}/status
Real-time elevator status

#### POST /elevators/{elevator_id}/maintenance
Schedule maintenance:
```json
{
  "maintenance_type": "preventive",
  "scheduled_date": "2024-02-01",
  "vendor_id": "uuid",
  "description": "Monthly maintenance check"
}
```

#### POST /elevators/{elevator_id}/incidents
Report elevator incident:
```json
{
  "incident_type": "breakdown",
  "description": "Elevator stuck between floors 5 and 6",
  "severity": "high",
  "reported_by": "uuid"
}
```

### 2.6 Access Card Management

#### GET /access-cards
List access cards with filters:
- `status`: active, inactive, lost, stolen
- `card_type`: resident, visitor, staff
- `holder_id`: specific user

#### POST /access-cards
Issue new access card:
```json
{
  "card_number": "CARD123456",
  "card_type": "resident",
  "holder_id": "uuid",
  "access_level": "basic",
  "expiry_date": "2025-01-01"
}
```

#### PUT /access-cards/{card_id}/status
Update card status (deactivate, report lost/stolen)

#### GET /access-cards/{card_id}/permissions
Get card access permissions

#### POST /access-cards/{card_id}/permissions
Grant access permissions:
```json
{
  "access_point_ids": ["uuid1", "uuid2"],
  "time_restrictions": {
    "monday": {"start": "06:00", "end": "22:00"},
    "tuesday": {"start": "06:00", "end": "22:00"}
  },
  "valid_from": "2024-01-01",
  "valid_until": "2024-12-31"
}
```

### 2.7 Facility & Amenity Management

#### GET /amenities
List building amenities

#### GET /amenities/{amenity_id}/availability
Check amenity availability for date range

#### POST /amenities/{amenity_id}/bookings
Book amenity:
```json
{
  "booking_date": "2024-02-15",
  "start_time": "14:00",
  "end_time": "16:00",
  "guest_count": 5,
  "special_requests": "Need projector setup"
}
```

#### GET /bookings
List user's bookings

#### DELETE /bookings/{booking_id}
Cancel booking

### 2.8 Maintenance Requests

#### GET /maintenance-requests
List maintenance requests with filters:
- `status`: open, assigned, in_progress, completed
- `category`: plumbing, electrical, hvac
- `priority`: low, medium, high, urgent

#### POST /maintenance-requests
Create maintenance request:
```json
{
  "unit_id": "uuid",
  "category": "plumbing",
  "priority": "high",
  "title": "Kitchen sink leak",
  "description": "Water leaking from under kitchen sink",
  "location": "Kitchen",
  "preferred_time": "Morning (9 AM - 12 PM)"
}
```

#### PUT /maintenance-requests/{request_id}/assign
Assign request to maintenance staff

#### POST /maintenance-requests/{request_id}/complete
Mark request as completed with notes

### 2.9 Communication

#### GET /announcements
List building announcements

#### POST /announcements
Create announcement:
```json
{
  "title": "Pool Maintenance Notice",
  "content": "Pool will be closed for maintenance...",
  "announcement_type": "maintenance",
  "priority": "normal",
  "target_audience": "all",
  "publish_date": "2024-01-15T09:00:00Z",
  "expiry_date": "2024-01-20T18:00:00Z",
  "send_email": true,
  "send_push": true
}
```

#### GET /messages
List user messages

#### POST /messages
Send message to user

### 2.10 Visitor Management

#### GET /visitors
List expected/current visitors

#### POST /visitors
Register visitor:
```json
{
  "host_resident_id": "uuid",
  "visitor_name": "Jane Smith",
  "visitor_phone": "+**********",
  "expected_arrival": "2024-01-15T14:00:00Z",
  "expected_departure": "2024-01-15T18:00:00Z",
  "visit_purpose": "Social visit",
  "vehicle_number": "ABC123"
}
```

#### PUT /visitors/{visitor_id}/check-in
Check in visitor

#### PUT /visitors/{visitor_id}/check-out
Check out visitor

## 3. Real-time Features

### 3.1 WebSocket Endpoints
- `/ws/notifications` - Real-time notifications
- `/ws/elevator-status` - Live elevator status updates
- `/ws/access-logs` - Real-time access events

### 3.2 Webhook Support
- Payment confirmations
- Maintenance request updates
- Emergency alerts
- System status changes

## 4. Response Formats

### 4.1 Success Response
```json
{
  "success": true,
  "data": { ... },
  "message": "Operation completed successfully",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### 4.2 Error Response
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "field": "email",
      "issue": "Invalid email format"
    }
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### 4.3 Pagination
```json
{
  "success": true,
  "data": [...],
  "pagination": {
    "page": 1,
    "per_page": 20,
    "total": 150,
    "total_pages": 8,
    "has_next": true,
    "has_prev": false
  }
}
```

## 5. Rate Limiting

- **General API**: 1000 requests/hour per user
- **Authentication**: 10 requests/minute per IP
- **File Upload**: 50 requests/hour per user
- **Real-time endpoints**: 100 connections per user

## 6. Security Headers

- `X-Building-ID`: Required for building-specific operations
- `X-Request-ID`: Unique request identifier for tracking
- `X-API-Version`: API version (default: v1)
- `X-Client-Type`: web, mobile, api (for analytics)

## 7. Error Codes

- `AUTH_REQUIRED`: Authentication required
- `INSUFFICIENT_PERMISSIONS`: User lacks required permissions
- `BUILDING_ACCESS_DENIED`: User cannot access specified building
- `RESOURCE_NOT_FOUND`: Requested resource not found
- `VALIDATION_ERROR`: Input validation failed
- `RATE_LIMIT_EXCEEDED`: Too many requests
- `MAINTENANCE_MODE`: System under maintenance
